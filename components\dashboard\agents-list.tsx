'use client';

import { useEffect, useState } from 'react';
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Plus } from "lucide-react";

interface Agent {
  _id: string;
  name: string;
  background: string;
}

export function AgentsList() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchAgents() {
      try {
        const response = await fetch('/api/agents');
        if (!response.ok) throw new Error('Failed to fetch agents');
        const data = await response.json();
        setAgents(data);
      } catch (error) {
        console.error('Error fetching agents:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchAgents();
  }, []);

  if (loading) return <div>Loading agents...</div>;

  return (
    <Card className="col-span-1 w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-8">
        <div>
          <CardTitle>AI Clients</CardTitle>
          {/* <CardDescription>Your AI therapy clients</CardDescription> */}
        </div>
        <Link href="/dashboard/session/new">
          <Button size="sm" className="px-3">
            <Plus className="mr-2 h-4 w-4" />
            Add New Client          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {agents.map((agent) => (
            <Link 
              key={agent._id} 
              href={`/dashboard/session/${agent._id}`}
              className="block"
            >
              <div className="flex items-center space-x-4 rounded-md border p-4 hover:bg-accent transition-colors w-full">
                <Avatar>
                  <AvatarFallback>
                    {agent.name.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-1 text-left">
                  <p className="text-sm font-medium leading-none">{agent.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {agent.background}
                  </p>
                </div>
              </div>
            </Link>
          ))}
          {agents.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              No AI clients yet. Create your first one to begin.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}