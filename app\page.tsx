"use client";

import Link from "next/link";
import Image from "next/image";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { Brain, MessageCircle, History, ChevronRight, Sparkles, Star, Shield } from "lucide-react";
import { ThemeToggle } from "@/components/theme-toggle";
import { LandingMobileNav } from "@/components/landing-mobile-nav";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";

export default function Home() {
  const { data: session } = useSession();
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch by only rendering after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  const isDark = mounted && theme === "dark";

  return (
    <div className={`min-h-screen ${isDark ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-black' : 'bg-gradient-to-br from-gray-100 via-gray-50 to-white'}`}>
      <div className={`absolute inset-0 ${isDark ? 'bg-grid-white/[0.05]' : 'bg-grid-black/[0.05]'} -z-10`} />

      <header className="container mx-auto px-4 py-6 flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className="relative">
            <div className={`absolute`}></div>
            <Image
              src="/logo.png"
              alt="AI Therapy Practice"
              width={172}
              height={172}
              className="relative object-contain"
            />
          </div>
          {/* <span className={`pl-2 text-xl font-bold ${isDark ? 'text-white' : 'text-black'}`}>
            AI Therapy Practice
          </span> */}
        </div>
        <div className="flex items-center gap-4">
          {/* Mobile Navigation - shows on mobile only */}
          <LandingMobileNav />

          {/* Desktop Navigation - hidden on mobile */}
          <div className="hidden md:flex items-center gap-4">
            <ThemeToggle variant={isDark ? "ghost" : "outline"} />
            {session ? (
              <Button asChild className={isDark ? "bg-white text-black hover:bg-gray-200" : "bg-black text-white hover:bg-gray-800"}>
                <Link href="/dashboard">Go to Dashboard</Link>
              </Button>
            ) : (
              <div className="flex gap-4">
                <Button variant="ghost" asChild className={isDark ? "text-white hover:bg-white/10" : "text-black hover:bg-black/10"}>
                  <Link href="/auth/signin">Sign In</Link>
                </Button>
                <Button asChild className={isDark ? "bg-white text-black hover:bg-gray-200" : "bg-black text-white hover:bg-gray-800"}>
                  <Link href="/auth/signup">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-20">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className={`text-4xl md:text-6xl font-bold tracking-tight ${isDark ? 'text-white' : 'text-gray-900'} mb-6 drop-shadow-lg`}>
            HeartWise 

 <span className="text-primary"> Ai Client</span>
          </h1>
          <p className={`text-xl ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-10`}>
            Enhance your therapeutic skills through realistic conversations
          </p>
          <div className="flex justify-center gap-4 mb-8">
            <Button
              asChild
              size="lg"
              className={` px-8 py-5 text-lg shadow-lg ${isDark ? "bg-white text-black hover:bg-gray-200" : "bg-black text-white hover:bg-gray-800"}`}
            >
              <Link href="/auth/signup">
                Get Started
                <ChevronRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>

       
      </main>
    </div>
  );
}