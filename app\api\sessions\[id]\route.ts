import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { connectDB } from "@/lib/mongodb";
import Session from "@/models/session";
import User from "@/models/user";
import { OpenAI } from "openai";
import { sendSessionFeedback } from "@/lib/email";

export const dynamic = 'force-dynamic';

if (!process.env.OPENAI_API_KEY) {
  throw new Error('OpenAI API key is not configured');
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function PATCH(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession();
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { messages, endTime, actualDuration } = await req.json();

    await connectDB();
    const sessionData = await Session.findById(params.id);
    if (!sessionData) {
      return NextResponse.json({ error: "Session not found" }, { status: 404 });
    }

    // Update session with end time and actual duration
    sessionData.endTime = new Date(endTime);
    sessionData.actualDuration = actualDuration;
    sessionData.messages = messages;

    // Create the feedback prompt
    const feedbackPrompt = `
Analyze the following hypnotherapy session and provide detailed feedback on the therapist's performance.
Focus on these specific areas and provide concrete examples from the conversation:

1. Rapport Building
   - Initial connection establishment
   - Trust development techniques
   - Non-verbal communication cues used
   - Empathy demonstration

2. Pacing and Leading
   - Matching client's energy/tone
   - Transition management
   - Timing of interventions
   - Flow control

3. Language Patterns
   - Use of hypnotic language
   - Metaphor utilization
   - Suggestion framing
   - Language clarity and effectiveness

4. Therapeutic Techniques
   - Appropriate technique selection
   - Implementation effectiveness
   - Adaptation to client responses
   - Integration of multiple approaches

5. Session Management
   - Time management
   - Goal setting and achievement
   - Session structure
   - Progress tracking

Session transcript:
${messages.map((msg: { role: string; content: string; }) => 
  `${msg.role === 'assistant' ? 'CLIENT' : 'THERAPIST'}: ${msg.content}`
).join('\n')}

Provide feedback in this exact JSON format:
{
  "strengths": [
    "Specific strength 1 with example",
    "Specific strength 2 with example",
    "Specific strength 3 with example"
  ],
  "improvements": [
    "Specific improvement 1 with suggestion",
    "Specific improvement 2 with suggestion",
    "Specific improvement 3 with suggestion"
  ],
  "overallRating": <number 1-5>,
  "comments": "Detailed paragraph analyzing the overall session, highlighting key observations and providing actionable recommendations for improvement."
}

Ensure each point in strengths and improvements is specific, actionable, and references actual moments from the session.
The overall rating should reflect: 
1 = Needs significant improvement
2 = Below average performance
3 = Average performance
4 = Above average performance
5 = Excellent performance

Return only valid JSON without any additional text or formatting.`;

    // Get AI feedback
    const completion = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert hypnotherapy instructor. You must respond with valid JSON only, following the exact structure: {\"strengths\": [], \"improvements\": [], \"overallRating\": number, \"comments\": string}. Do not include any other text or formatting. Remember that in the conversation, the user is the therapist and the AI is the client."
        },
        {
          role: "user",
          content: feedbackPrompt
        }
      ],
      temperature: 0.7
    });

    // Parse the response, with error handling
    try {
      const responseContent = completion.choices[0].message.content;
      if (!responseContent) {
        throw new Error('Empty response from OpenAI');
      }

      // Clean the response string to ensure it's valid JSON
      const cleanedContent = responseContent.trim().replace(/```json|```/g, '');
      const feedback = JSON.parse(cleanedContent);
      
      // Validate feedback structure
      if (!feedback.strengths || !feedback.improvements || !feedback.overallRating || !feedback.comments) {
        throw new Error('Invalid feedback format');
      }

      // Validate data types
      if (!Array.isArray(feedback.strengths) || !Array.isArray(feedback.improvements)) {
        throw new Error('Strengths and improvements must be arrays');
      }
      if (typeof feedback.overallRating !== 'number' || feedback.overallRating < 1 || feedback.overallRating > 5) {
        throw new Error('Overall rating must be a number between 1 and 5');
      }
      if (typeof feedback.comments !== 'string') {
        throw new Error('Comments must be a string');
      }
      
      console.log("Saving session with feedback:", {
        messages: messages.length,
        feedback: feedback
      });

      // Update session with messages and feedback
      sessionData.messages = messages;
      sessionData.feedback = feedback;
      await sessionData.save();

      // Send email with feedback
      try {
        await sendSessionFeedback({
          to: session.user.email,
          sessionData: {
            date: new Date(sessionData.startTime).toLocaleDateString(),
            duration: `${Math.round(sessionData.duration / 60)} minutes`,
            rating: feedback.overallRating,
            strengths: feedback.strengths,
            improvements: feedback.improvements,
            summary: feedback.comments
          }
        });
      } catch (emailError) {
        console.error("Error sending feedback email:", emailError);
        // Don't fail the request if email fails
      }

      return NextResponse.json({ feedback });
    } catch (error) {
      console.error("Error parsing feedback:", error);
      return NextResponse.json(
        { error: "Failed to generate valid feedback" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error ending session:", error);
    return NextResponse.json(
      { error: "Failed to end session" },
      { status: 500 }
    );
  }
}
