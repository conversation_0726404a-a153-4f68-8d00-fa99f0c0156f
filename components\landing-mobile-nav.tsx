"use client";

import { useState } from "react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { ThemeToggle } from "@/components/theme-toggle";
import { useTheme } from "next-themes";

export function LandingMobileNav() {
  const [open, setOpen] = useState(false);
  const { data: session } = useSession();
  const { theme } = useTheme();
  const isDark = theme === "dark";

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className={`h-6 w-6 ${isDark ? 'text-white' : 'text-black'}`} />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-72">
        <SheetHeader>
          <SheetTitle className="text-left">Menu</SheetTitle>
        </SheetHeader>
        <div className="flex flex-col space-y-4 mt-6">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Theme</span>
            <ThemeToggle />
          </div>
          
          <div className="border-t pt-4 space-y-3">
            {session ? (
              <Button 
                asChild 
                className="w-full"
                onClick={() => setOpen(false)}
              >
                <Link href="/dashboard">Go to Dashboard</Link>
              </Button>
            ) : (
              <div className="space-y-3">
                <Button 
                  variant="outline" 
                  asChild 
                  className="w-full"
                  onClick={() => setOpen(false)}
                >
                  <Link href="/auth/signin">Sign In</Link>
                </Button>
                <Button 
                  asChild 
                  className="w-full"
                  onClick={() => setOpen(false)}
                >
                  <Link href="/auth/signup">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
