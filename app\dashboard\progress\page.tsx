'use client';

import { useEffect, useState } from 'react';
import { useTheme } from "next-themes";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

interface WeeklyData {
  week: string;
  sessions: number;
  hours: number;
  rating: number;
}

interface SkillData {
  skill: string;
  score: number;
}

export default function ProgressPage() {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [weeklyData, setWeeklyData] = useState<WeeklyData[]>([]);
  const [skillsData, setSkillsData] = useState<SkillData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchProgress() {
      try {
        const response = await fetch('/api/progress');
        if (!response.ok) throw new Error('Failed to fetch progress data');
        const data = await response.json();
        setWeeklyData(data.weeklyData);
        setSkillsData(data.skillsData);
      } catch (error) {
        console.error('Error fetching progress:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchProgress();
    setMounted(true);
  }, []);

  if (!mounted) return null;
  const colors = getThemeColors();

  if (loading) {
    return <div>Loading progress data...</div>;
  }

  function getThemeColors() {
    const isDark = theme === 'dark';
    return {
      text: isDark ? '#ffffff' : '#000000',
      grid: isDark ? '#333333' : '#e5e5e5',
      bar: isDark ? 'hsl(var(--primary))' : 'hsl(var(--primary))',
      tooltip: isDark ? '#1f1f1f' : '#ffffff',
    };
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Progress Tracking</h1>
        <p className="text-muted-foreground">
          Monitor your development as a hypnotherapist
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-4">
        <Card>
          <CardHeader>
            <CardTitle>Session Frequency</CardTitle>
            <CardDescription>Sessions per week</CardDescription>
          </CardHeader>
          <CardContent className="h-[250px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData} barSize={40} maxBarSize={40}>
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke={colors.grid}
                />
                <XAxis
                  dataKey="week"
                  stroke={colors.text}
                />
                <YAxis
                  stroke={colors.text}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: colors.tooltip,
                    border: 'none',
                  }}
                  labelStyle={{ color: colors.text }}
                />
                <Bar
                  dataKey="sessions"
                  fill={colors.bar}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Practice Hours</CardTitle>
            <CardDescription>Total hours per week</CardDescription>
          </CardHeader>
          <CardContent className="h-[250px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData} barSize={40} maxBarSize={40}>
                <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                <XAxis dataKey="week" stroke={colors.text} />
                <YAxis stroke={colors.text} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: colors.tooltip,
                    border: 'none',
                  }}
                  labelStyle={{ color: colors.text }}
                />
                <Bar
                  dataKey="hours"
                  fill={colors.bar}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Rating</CardTitle>
            <CardDescription>Average rating per week</CardDescription>
          </CardHeader>
          <CardContent className="h-[250px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData} barSize={40} maxBarSize={40}>
                <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                <XAxis dataKey="week" stroke={colors.text} />
                <YAxis
                  domain={[0, 5]}
                  ticks={[0, 1, 2, 3, 4, 5]}
                  stroke={colors.text}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: colors.tooltip,
                    border: 'none',
                  }}
                  labelStyle={{ color: colors.text }}
                />
                <Bar
                  dataKey="rating"
                  fill={colors.bar}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Skills Assessment</CardTitle>
            <CardDescription>Skill rating breakdown</CardDescription>
          </CardHeader>
          <CardContent className="h-[250px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={skillsData}
                layout="vertical"
                barSize={20}
                maxBarSize={20}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke={colors.grid} />
                <XAxis type="number" domain={[0, 100]} stroke={colors.text} />
                <YAxis
                  dataKey="skill"
                  type="category"
                  width={150}
                  stroke={colors.text}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: colors.tooltip,
                    border: 'none',
                  }}
                  labelStyle={{ color: colors.text }}
                />
                <Bar
                  dataKey="score"
                  fill={colors.bar}
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}