'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Star, Download, InfoIcon, TrendingUp, UserPlus, LayoutDashboard } from 'lucide-react';
import { generateFeedbackPDF } from "@/lib/pdf";
import Link from 'next/link';

interface Session {
  _id: string;
  agentId: {
    _id: string;
    name: string;
    background: string;
  };
  startTime: string;
  duration: number;
  messages: Array<{
    role: string;
    content: string;
    timestamp: string;
  }>;
  feedback?: {
    strengths: string[];
    improvements: string[];
    overallRating: number;
    comments: string;
  };
}

export default function HistoryPage() {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDuration, setFilterDuration] = useState('all');
  const [selectedFeedback, setSelectedFeedback] = useState<Session['feedback'] | null>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSessions() {
      try {
        setLoading(true);
        const response = await fetch('/api/sessions');
        if (!response.ok) throw new Error('Failed to fetch sessions');
        const data = await response.json();
        console.log("Fetched sessions:", data); // Debug log
        setSessions(data);
      } catch (error) {
        console.error('Error fetching sessions:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch sessions');
      } finally {
        setLoading(false);
      }
    }

    fetchSessions();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    return `${minutes} min`;
  };

  const filteredSessions = sessions.filter((session) => {
    const matchesSearch = session.agentId?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ?? true;
    const matchesDuration = filterDuration === 'all' ||
      (filterDuration === '30' && session.duration <= 1800) ||
      (filterDuration === '45' && session.duration > 1800 && session.duration <= 2700) ||
      (filterDuration === '60' && session.duration > 2700);
    return matchesSearch && matchesDuration;
  });

  return (
    <div className="space-y-8 px-4 sm:px-0 mt-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Session History</h1>
        <p className="text-muted-foreground">View and analyze your past practice sessions</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          {/* <CardDescription>Refine your session history</CardDescription> */}
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={filterDuration} onValueChange={setFilterDuration}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All durations</SelectItem>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="45">45 minutes</SelectItem>
                <SelectItem value="60">60 minutes</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Sessions</CardTitle>
          {/* <CardDescription>Your complete history of practice sessions</CardDescription> */}
        </CardHeader>
        <CardContent>
          {loading && <div>Loading sessions...</div>}
          {error && <div className="text-red-500">Error: {error}</div>}
          {!loading && !error && sessions.length === 0 && (
            <div>No sessions found. Start a new session to see your history.</div>
          )}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSessions.map((session) => (
                <TableRow key={session._id}>
                  <TableCell>{formatDate(session.startTime)}</TableCell>
                  <TableCell>{session.agentId?.name || 'Unknown Client'}</TableCell>
                  <TableCell>{formatDuration(session.duration)}</TableCell>
                  <TableCell>
                    {session.feedback?.overallRating ?
                      `${session.feedback.overallRating}/5` :
                      'No rating'
                    }
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedFeedback(session.feedback || null)}
                      disabled={!session.feedback}
                    >
                      View Feedback
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Dialog open={!!selectedFeedback} onOpenChange={() => setSelectedFeedback(null)}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle>Session Feedback</DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => generateFeedbackPDF(selectedFeedback!, 'Session Feedback')}
            >
              <Download className="h-4 w-4" />
            </Button>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto pr-6">
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-lg">Overall Rating</h3>
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < (selectedFeedback?.overallRating || 0)
                          ? "text-yellow-500 fill-yellow-500"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-lg">Strengths</h3>
                <ul className="list-disc pl-5">
                  {selectedFeedback?.strengths?.map((strength, i) => (
                    <li key={i}>{strength}</li>
                  )) || <li>No strengths recorded</li>}
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-lg">Areas for Improvement</h3>
                <ul className="list-disc pl-5">
                  {selectedFeedback?.improvements?.map((improvement, i) => (
                    <li key={i}>{improvement}</li>
                  )) || <li>No improvements recorded</li>}
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-lg">Detailed Feedback</h3>
                <p className="text-muted-foreground">
                  {selectedFeedback?.comments || 'No detailed feedback available'}
                </p>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-semibold text-lg mb-2">Recommended Next Steps</h3>
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  {selectedFeedback?.improvements?.map((improvement, i) => (
                    <li key={i}>Practice: {improvement.split(":")[0]}</li>
                  ))}
                  <li>Review the detailed feedback and focus on one area at a time</li>
                  <li>Consider practicing with different client personalities to broaden your skills</li>
                </ul>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-semibold text-lg mb-3">Quick Actions</h3>
                <div className="grid grid-cols-2 gap-3">
                  <Link href="/dashboard/progress">
                    <Button variant="outline" className="w-full">
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Check Your Progress
                    </Button>
                  </Link>
                  <Link href="/dashboard/session/new">
                    <Button variant="outline" className="w-full">
                      <UserPlus className="mr-2 h-4 w-4" />
                      Start New Session
                    </Button>
                  </Link>
                  <Link href="/dashboard">
                    <Button variant="outline" className="w-full">
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      Return to Dashboard
                    </Button>
                  </Link>
                </div>
              </div>

              <div className="bg-muted p-4 rounded-lg mt-4">
                <p className="text-sm text-muted-foreground">
                  <InfoIcon className="inline-block h-4 w-4 mr-2" />
                  Regular review of past sessions can help identify patterns and track your improvement over time.
                  Use the Progress section to see your skill development across different areas.
                </p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}