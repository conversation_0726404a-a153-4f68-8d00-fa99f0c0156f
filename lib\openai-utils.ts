import OpenAI from 'openai';

// if (!process.env.OPENAI_API_KEY) {
//   throw new Error('OpenAI API key is not configured');
// }
const OPENAI_API_KEY="********************************************************************************************************************************************************************"

const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Only for client-side usage
});

export async function enhanceGreeting(greeting: string): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant that improves and enhances text while preserving all placeholders in the format ${...}.'
        },
        {
          role: 'user',
          content: `Please enhance this greeting to sound more natural and professional while keeping all placeholders exactly as they are. Only respond with the enhanced greeting, nothing else. Here's the greeting: "${greeting}"`
        }
      ],
      temperature: 0.7,
      max_tokens: 200
    });

    return response.choices[0]?.message?.content?.trim() || greeting;
  } catch (error) {
    console.error('Error enhancing greeting with OpenAI:', error);
    return greeting; // Return original greeting if there's an error
  }
}
