'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { enhanceGreeting } from '@/lib/openai-utils';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { Timer, Send, Volume2, Play, Mic, Loader2, Download, History, TrendingUp, UserPlus, LayoutDashboard, InfoIcon, Clock, Settings } from 'lucide-react';
import { VoiceRecorder } from '@/components/chat/voice-recorder';
import styles from './SessionPage.module.css'; // Import CSS module
import { textToSpeech } from '@/lib/elevenlabs'; // Import ElevenLabs utility
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Star } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { generateFeedbackPDF } from "@/lib/pdf"; // We'll create this
import Link from 'next/link';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface Agent {
  _id: string;
  name: string;
  background: string;
  personality: string;
  concerns: string[];
  gender: 'male' | 'female';
}

interface Feedback {
  strengths: string[];
  improvements: string[];
  overallRating: number;
  comments: string;
}

// Duration options in minutes
const DURATION_OPTIONS = [
  { value: '15', label: '15 minutes', seconds: 15 * 60 },
  { value: '30', label: '30 minutes', seconds: 30 * 60 },
  { value: '45', label: '45 minutes', seconds: 45 * 60 },
  { value: '60', label: '60 minutes', seconds: 60 * 60 },
  { value: '90', label: '90 minutes', seconds: 90 * 60 },
];

export default function SessionPage() {
  const params = useParams();
  const pathname = usePathname();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [timeLeft, setTimeLeft] = useState(0);
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [agent, setAgent] = useState<Agent | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(true);
  // Set default voice based on agent's gender
  const [selectedVoice, setSelectedVoice] = useState<string>('rubi'); // Default to Clay (rubi)

  // Update selected voice when agent changes
  useEffect(() => {
    if (agent) {
      setSelectedVoice(agent.gender === 'male' ? 'peter' : 'rubi');
    }
  }, [agent]);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [isFeedbackLoading, setIsFeedbackLoading] = useState(false);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  const [actualDuration, setActualDuration] = useState(0);
  const [sessionDuration, setSessionDuration] = useState('60'); // Default to 60 minutes

  useEffect(() => {
    async function fetchAgent() {
      try {
        const response = await fetch(`/api/agents/${params.id}`);
        if (!response.ok) throw new Error('Failed to fetch agent');
        const data = await response.json();
        setAgent(data);
      } catch (error) {
        console.error('Error fetching agent:', error);
      }
    }

    fetchAgent();
  }, [params.id]);

  useEffect(() => {
    async function fetchConversation() {
      try {
        const response = await fetch(`/api/conversations/${params.id}`);
        if (!response.ok) throw new Error('Failed to fetch conversation');
        const data = await response.json();
        setMessages(data.messages);
      } catch (error) {
        console.error('Error fetching conversation:', error);
      }
    }

    if (pathname.includes('/dashboard/history')) {
      fetchConversation();
    }
  }, [params.id, pathname]);

  useEffect(() => {
    if (timeLeft > 0 && isSessionActive) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    } else if (timeLeft === 0 && isSessionActive) {
      endSession();
    }
  }, [timeLeft, isSessionActive]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    // Reset textarea height when input is cleared
    if (!input && textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  }, [input]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const playGreeting = async (content: string) => {
    if (!isVoiceEnabled) return;
    
    try {
      const voiceId = selectedVoice === 'peter'
        ? process.env.NEXT_PUBLIC_PEACEFUL_PETER
        : process.env.NEXT_PUBLIC_RUBI;
      if (!voiceId) {
        throw new Error('Voice ID is not defined');
      }
      const audioUrl = await textToSpeech(content, voiceId);
      const audio = new Audio(audioUrl);
      await audio.play();
    } catch (error) {
      console.error('Error playing greeting message:', error);
      throw error; // Re-throw to handle in the calling function
    }
  };

  const getCurrentDurationLabel = () => {
    const option = DURATION_OPTIONS.find(opt => opt.value === sessionDuration);
    return option?.label || '60 minutes';
  };

  const startSession = async () => {
    try {
      const startTime = new Date();
      setSessionStartTime(startTime);

      // Get selected duration in seconds
      const selectedDurationOption = DURATION_OPTIONS.find(option => option.value === sessionDuration);
      const durationInSeconds = selectedDurationOption?.seconds || 60 * 60; // Default to 60 minutes

      const response = await fetch('/api/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId: agent?._id,
          duration: durationInSeconds,
          startTime: startTime,
        }),
      });

      if (!response.ok) throw new Error('Failed to create session');
      const data = await response.json();
      setSessionId(data._id);
      setIsSessionActive(true);
      setTimeLeft(durationInSeconds);
      
      // Generate and enhance the greeting message
      const baseGreeting = `Hello, I'm ${agent?.name}. ${agent?.background} I've been struggling with ${agent?.concerns?.[0] || 'some personal issues'} and I'm hoping hypnotherapy might help me find some relief. How can you help me today?`;
      
      try {
        const enhancedGreeting = await enhanceGreeting(baseGreeting);
        const greeting: Message = {
          role: 'assistant',
          content: enhancedGreeting,
          timestamp: new Date(),
        };
        setMessages([greeting]);
        
        // Use the enhanced greeting for TTS
        await playGreeting(enhancedGreeting);
      } catch (error) {
        console.error('Error enhancing greeting:', error);
        // Fallback to the original greeting if there's an error
        const greeting: Message = {
          role: 'assistant',
          content: baseGreeting,
          timestamp: new Date(),
        };
        setMessages([greeting]);
        await playGreeting(baseGreeting);
      }
    } catch (error) {
      console.error('Error starting session:', error);
    }
  };

  const sendMessage = async (messageContent: string) => {
    if (!messageContent.trim() || !isSessionActive) return;

    const userMessage: Message = {
      role: 'user',
      content: messageContent,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
    setIsTyping(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: messageContent,
          agentId: params.id,
          sessionId: sessionId,
          agentBackground: agent?.background,
          agentPersonality: agent?.personality,
          agentConcerns: agent?.concerns,
          conversationHistory: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
        }),
      });

      if (!response.ok) throw new Error('Failed to send message');

      const data = await response.json();
      const aiMessage: Message = {
        role: 'assistant',
        content: data.message,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, aiMessage]);

      if (isVoiceEnabled) {
        try {
          const voiceId = selectedVoice === 'peter'
            ? process.env.NEXT_PUBLIC_PEACEFUL_PETER
            : process.env.NEXT_PUBLIC_RUBI;
          if (!voiceId) {
            throw new Error('Voice ID is not defined');
          }
          const audioUrl = await textToSpeech(data.message, voiceId);
          const audio = new Audio(audioUrl);
          audio.play();
        } catch (error) {
          console.error('Error playing voice response:', error);
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsTyping(false);
    }
  };

  const handleTranscription = (text: string) => {
    sendMessage(text);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(input);
    }
  };

  const endSession = async () => {
    if (!sessionStartTime) return;

    const endTime = new Date();
    // Calculate duration in minutes instead of seconds
    const durationInMinutes = Math.floor((endTime.getTime() - sessionStartTime.getTime()) / (1000 * 60));
    setActualDuration(durationInMinutes);

    setIsSessionActive(false);
    setIsFeedbackLoading(true);
    try {
      const response = await fetch(`/api/sessions/${sessionId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages,
          endTime,
          actualDuration: durationInMinutes * 60 // Convert to seconds for storage
        }),
      });

      if (!response.ok) throw new Error('Failed to end session');

      const data = await response.json();
      setFeedback(data.feedback);
      setShowFeedback(true);
    } catch (error) {
      console.error('Error ending session:', error);
      toast({
        title: "Error",
        description: "Failed to generate session feedback",
        variant: "destructive",
        message: "Failed to generate session feedback",
      });
    } finally {
      setIsFeedbackLoading(false);
    }
  };

  const handleToggleVoice = () => {
    setIsVoiceEnabled(!isVoiceEnabled);
  };

  if (!agent) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex h-[calc(100vh-8rem)] flex-col space-y-4 px-6 sm:px-8">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="min-w-0 flex-1 p-4">
          <h1 className="text-xl sm:text-2xl font-bold truncate">Session with {agent.name}</h1>
          <p className="text-muted-foreground text-sm sm:text-base line-clamp-2">{agent.background}</p>
        </div>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
          {/* Top row on mobile: Voice toggle and timer/duration */}
          <div className="flex items-center gap-3 w-full sm:w-auto">
            <button
              onClick={handleToggleVoice}
              className="p-2 rounded-lg hover:bg-muted transition-colors"
            >
              <Mic className={`h-5 w-5 sm:h-6 sm:w-6 ${isVoiceEnabled ? 'text-blue-500' : 'text-gray-500'}`} />
            </button>

            {/* Voice Selection Dropdown */}
            {isVoiceEnabled && (
              <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Select voice" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="peter">{agent?.gender === 'male' ? 'Sam (Male)' : 'Sam'}</SelectItem>
                  <SelectItem value="rubi">{agent?.gender === 'female' ? 'Clay (Female)' : 'Clay'}</SelectItem>
                </SelectContent>
              </Select>
            )}

            {/* Session Duration Selector - only show when session is not active */}
            {!isSessionActive && (
              <div className="flex items-center gap-2 flex-1 sm:flex-initial">
                <Clock className="h-4 w-4 flex-shrink-0" />
                <Select value={sessionDuration} onValueChange={setSessionDuration}>
                  <SelectTrigger className="w-full sm:w-[140px]">
                    <SelectValue placeholder="Duration" />
                  </SelectTrigger>
                  <SelectContent>
                    {DURATION_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Timer display - only show when session is active */}
            {isSessionActive && (
              <div className="flex items-center gap-2 sm:gap-4 flex-1 sm:flex-initial">
                <div className="flex items-center gap-2">
                  <Timer className="h-4 w-4" />
                  <span className="font-mono text-sm sm:text-base">{formatTime(timeLeft)}</span>
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground hidden sm:block">
                  ({getCurrentDurationLabel()})
                </div>
              </div>
            )}
          </div>

          {/* Bottom row on mobile: Action button */}
          <div className="w-full sm:w-auto">
            {!isSessionActive ? (
              <Button
                onClick={startSession}
                style={{ backgroundColor: '#FFEB80', color: 'black' }}
                className="w-full sm:w-auto hover:bg-green-700"
                size="sm"
              >
                <Play className="mr-2 h-4 w-4" />
                Start Session
              </Button>
            ) : (
              <Button
                variant="destructive"
                onClick={endSession}
                className="w-full sm:w-auto"
                size="sm"
              >
                End Session
              </Button>
            )}
          </div>
        </div>
      </div>

      <Card className="flex-1 overflow-hidden p-4 sm:p-6">
        <div className="h-full overflow-y-auto space-y-4 sm:space-y-6 pr-4 sm:pr-6">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`${styles.message} flex ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`rounded-lg px-3 py-2 sm:px-4 sm:py-2 max-w-[85%] sm:max-w-[80%] ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                <p className="whitespace-pre-wrap text-sm sm:text-base leading-relaxed">{message.content}</p>
                <span className="text-xs opacity-70 mt-1 block">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </span>
              </div>
            </div>
          ))}
          {isTyping && (
            <div className={`${styles.typing} flex justify-start`}>
              <div className="rounded-lg px-3 py-2 sm:px-4 sm:py-2 max-w-[85%] sm:max-w-[80%] bg-muted">
                <p className="whitespace-pre-wrap text-sm sm:text-base">...</p>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </Card>

      <div className="flex flex-col gap-3 sm:gap-4">
        <div className="flex gap-2 sm:gap-4">
          <Textarea
            ref={textareaRef}
            value={input}
            onChange={handleInputChange}
            placeholder="Type your message... (Enter to send)"
            onKeyDown={handleKeyPress}
            disabled={!isSessionActive}
            className="text-sm sm:text-base min-h-[40px] max-h-[200px] resize-none overflow-y-auto"
            rows={1}
          />
          <div className="flex gap-2">
            <VoiceRecorder onTranscription={handleTranscription} />
            <Button
              onClick={() => sendMessage(input)}
              disabled={!isSessionActive}
              size="sm"
              className="px-3 sm:px-4"
            >
              <Send className="h-4 w-4" />
              <span className="sr-only">Send message</span>
            </Button>
          </div>
        </div>

        <div className="text-center text-xs text-muted-foreground">
          Press Enter to send • Shift+Enter for new line
        </div>

        {/* <div className="text-center text-xs sm:text-sm text-muted-foreground bg-muted p-2 sm:p-3 rounded-md">
          ⚠️ Disclaimer: This AI is for relaxation and self-reflection only. It is not a substitute for professional mental health care.
        </div> */}
      </div>

      {feedback && showFeedback && (
        <Dialog open={showFeedback} onOpenChange={setShowFeedback}>
          <DialogContent className="max-w-3xl max-h-[85vh] overflow-hidden flex flex-col">
            <DialogHeader className="flex flex-row items-center justify-between pb-4 border-b">
              <div>
                <DialogTitle className="text-2xl font-bold">Session Feedback</DialogTitle>
                <DialogDescription className="text-muted-foreground">
                  Detailed analysis of your therapy session with {agent?.name}
                </DialogDescription>
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={() => generateFeedbackPDF(feedback, agent?.name || 'Client')}
                className="hover:bg-primary hover:text-primary-foreground transition-colors"
              >
                <Download className="h-4 w-4" />
              </Button>
            </DialogHeader>

            <Tabs defaultValue="overview" className="flex-1 overflow-hidden flex flex-col">
              <TabsList className="grid w-full grid-cols-4 mb-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="strengths">Strengths</TabsTrigger>
                <TabsTrigger value="improvements">Improvements</TabsTrigger>
                <TabsTrigger value="next-steps">Next Steps</TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-y-auto pr-6">
                <TabsContent value="overview" className="space-y-6">
                  <div className="bg-muted/50 rounded-lg p-6">
                    <h3 className="font-semibold text-lg mb-4">Overall Performance</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">Overall Rating</span>
                          <span className="text-sm text-muted-foreground">{feedback.overallRating}/5</span>
                        </div>
                        {/* <Progress value={feedback.overallRating * 20} className="h-2" /> */}
                      </div>
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-6 w-6 ${
                              i < feedback.overallRating
                                ? "text-yellow-500 fill-yellow-500"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="bg-muted/50 rounded-lg p-6">
                    <h3 className="font-semibold text-lg mb-4">Session Summary</h3>
                    <p className="text-muted-foreground leading-relaxed">{feedback.comments}</p>
                  </div>
                </TabsContent>

                <TabsContent value="strengths" className="space-y-6">
                  <div className="bg-muted/50 rounded-lg p-6">
                    <h3 className="font-semibold text-lg mb-4">Key Strengths</h3>
                    <div className="grid gap-4">
                      {feedback.strengths.map((strength, i) => (
                        <div key={i} className="flex items-start gap-3">
                          <div className="bg-green-500/10 text-green-500 rounded-full p-2">
                            <TrendingUp className="h-4 w-4" />
                          </div>
                          <p className="text-muted-foreground">{strength}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="improvements" className="space-y-6">
                  <div className="bg-muted/50 rounded-lg p-6">
                    <h3 className="font-semibold text-lg mb-4">Areas for Improvement</h3>
                    <div className="grid gap-4">
                      {feedback.improvements.map((improvement, i) => (
                        <div key={i} className="flex items-start gap-3">
                          <div className="bg-blue-500/10 text-blue-500 rounded-full p-2">
                            <Settings className="h-4 w-4" />
                          </div>
                          <p className="text-muted-foreground">{improvement}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="next-steps" className="space-y-6">
                  <div className="bg-muted/50 rounded-lg p-6">
                    <h3 className="font-semibold text-lg mb-4">Recommended Next Steps</h3>
                    <div className="grid gap-4">
                      {feedback.improvements.map((improvement, i) => (
                        <div key={i} className="flex items-start gap-3">
                          <Badge variant="outline" className="bg-primary/5">
                            Practice
                          </Badge>
                          <p className="text-muted-foreground">{improvement.split(":")[0]}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <Link href="/dashboard/history">
                      <Button variant="outline" className="w-full hover:bg-primary hover:text-primary-foreground transition-colors">
                        <History className="mr-2 h-4 w-4" />
                        View History
                      </Button>
                    </Link>
                    <Link href="/dashboard/progress">
                      <Button variant="outline" className="w-full hover:bg-primary hover:text-primary-foreground transition-colors">
                        <TrendingUp className="mr-2 h-4 w-4" />
                        Check Progress
                      </Button>
                    </Link>
                    <Link href="/dashboard/session/new">
                      <Button variant="outline" className="w-full hover:bg-primary hover:text-primary-foreground transition-colors">
                        <UserPlus className="mr-2 h-4 w-4" />
                        New Session
                      </Button>
                    </Link>
                    <Link href="/dashboard">
                      <Button variant="outline" className="w-full hover:bg-primary hover:text-primary-foreground transition-colors">
                        <LayoutDashboard className="mr-2 h-4 w-4" />
                        Dashboard
                      </Button>
                    </Link>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </DialogContent>
        </Dialog>
      )}

      {isFeedbackLoading && (
        <Dialog open={isFeedbackLoading} onOpenChange={() => {}}>
          <DialogContent className="sm:max-w-md">
            <div className="flex flex-col items-center space-y-4 py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <h3 className="text-lg font-semibold">Generating Feedback</h3>
              <p className="text-center text-muted-foreground">
                Please wait while we analyze your session and generate detailed feedback...
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
