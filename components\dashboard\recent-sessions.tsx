"use client";

import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Star } from "lucide-react";

interface RecentSession {
  id: string;
  date: string;
  agentName: string;
  duration: number;
  rating: number;
}

export function RecentSessions() {
  const [sessions, setSessions] = useState<RecentSession[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchSessions() {
      try {
        const response = await fetch('/api/dashboard');
        if (!response.ok) throw new Error('Failed to fetch sessions');
        const data = await response.json();
        setSessions(data.recentSessions);
      } catch (error) {
        console.error('Error fetching sessions:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchSessions();
  }, []);

  if (loading) return <div>Loading recent sessions...</div>;

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Recent Sessions</CardTitle>
        {/* <CardDescription>Your latest practice sessions</CardDescription> */}
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {sessions.map((session) => (
            <div key={session.id} className="flex items-center space-x-4 rounded-md border p-4">
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium leading-none">
                  {session.agentName}
                </p>
                <p className="text-sm text-muted-foreground">
                  {session.date} • {session.duration} min
                </p>
              </div>
              <div className="flex items-center gap-1 bg-primary/10 px-3 py-1 rounded-full">
                <Star className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium text-primary">
                  {session.rating}/5
                </span>
              </div>
            </div>
          ))}
          {sessions.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              No recent sessions. Start practicing to see your history.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}