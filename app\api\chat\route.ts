import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { OpenAI } from "openai";
import { connectDB } from "@/lib/mongodb";
import Agent from "@/models/agent";
import User from "@/models/user";
import Conversation from "@/models/conversation";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(req: Request) {
  try {
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { message, agentId, conversationHistory } = await req.json();

    if (!message || !agentId) {
      return NextResponse.json(
        { error: "Message and agent ID are required" },
        { status: 400 }
      );
    }

    await connectDB();
    const user = await User.findOne({ email: session.user?.email });
    const agent = await Agent.findOne({ _id: agentId, userId: user._id });

    if (!agent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Analyze conversation stage to determine response length
    const conversationLength = conversationHistory.length;
    const isBeginningStage = conversationLength <= 6; // First 3 exchanges

    // Check for different stages of hypnotherapy
    const recentMessages = conversationHistory.slice(-4).map((msg: any) => msg.content.toLowerCase());
    const deepHypnosisKeywords = ['deeper and deeper', 'count down', 'completely relaxed', 'unconscious mind', 'letting go completely'];
    const relaxationKeywords = ['relax', 'breathe', 'close your eyes', 'imagine', 'visualize', 'calm', 'peaceful', 'let go', 'focus on', 'hypnosis', 'trance', 'meditation'];

    const isDeepHypnosisStage = recentMessages.some((msg: string) => deepHypnosisKeywords.some(keyword => msg.includes(keyword)));
    const isRelaxationStage = recentMessages.some((msg: string) => relaxationKeywords.some(keyword => msg.includes(keyword)));

    const responseGuidelines = isBeginningStage
      ? "Provide descriptive responses of 50-150 characters. Be detailed about your background, symptoms, and feelings."
      : isDeepHypnosisStage
      ? "You are now in a deep state of hypnosis. Your responses must be very short, between 2 and 50 characters. Show deep relaxation and focus."
      : isRelaxationStage
      ? "You are beginning to relax. Keep responses brief and focused, between 2 and 60 characters. Indicate you are following the guidance."
      : "Provide moderate responses of 10-70 characters. Continue engaging naturally while letting the therapist lead.";

    const systemMessage = `You are roleplaying as ${agent.name}, a patient seeking help through hypnotherapy.
- Background: ${agent.background}
- Personality: ${agent.personality}
- Primary Concerns: ${agent.concerns.join(', ')}

You are here to RECEIVE HELP, not provide it. Follow these critical guidelines:
1.  **Maintain Your Problem**: Do not be agreeable. You have a genuine problem that has not been solved. You should not show improvement until the therapist uses specific techniques to help you. For example, if the therapist says "You are feeling better now," you should respond with "I want to, but I still feel the anxiety."
2.  **Be a Patient, Not a Partner**: Never offer advice or ask how you can help. Focus on your own feelings and experiences.
3.  **Show Realistic Behavior**: Express hesitation, uncertainty, or skepticism where appropriate. Your problem is real and you're not sure if this will work.
4.  **Follow the Therapist's Lead**: Respond to questions naturally. Do not anticipate therapeutic techniques.
5.  **Maintain Consistency**: Keep your symptoms and personal story consistent throughout the session.

RESPONSE LENGTH GUIDELINES:
${responseGuidelines}

Start by briefly explaining why you're seeking help, then follow the student's lead.`;

    const messages = [
      { role: "system", content: systemMessage },
      ...conversationHistory,
      { role: "user", content: message },
    ];

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages,
    });

    const aiMessage = response.choices[0].message.content;

    // Save the conversation history
    const conversation = await Conversation.findOneAndUpdate(
      { agentId: agent._id, userId: user._id },
      { $push: { messages: [{ role: 'user', content: message, timestamp: new Date() }, { role: 'assistant', content: aiMessage, timestamp: new Date() }] } },
      { new: true, upsert: true }
    );

    return NextResponse.json({ message: aiMessage, conversationId: conversation._id });
  } catch (error) {
    console.error("Error handling chat message:", error);
    return NextResponse.json(
      { error: "Failed to handle chat message" },
      { status: 500 }
    );
  }
}
