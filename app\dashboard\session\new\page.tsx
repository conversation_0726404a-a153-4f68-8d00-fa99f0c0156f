"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  behavior: z.string().min(10, "Behavior must be at least 10 characters"),
  gender: z.enum(['male', 'female']),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  sessionDuration: z.string().min(1, "Please select a duration"),
});

const defaultPatients = [
  // Male Personas
  {
    name: "Mark",
    description: "Recently divorced, struggling with loneliness.",
    behavior: "You're adjusting to life after a recent divorce. You feel a deep sense of loneliness, particularly in social settings and when you're alone at home. You struggle with the emotional toll of the divorce, often questioning your self-worth. You find it hard to open up to others and avoid discussing your feelings, leading to increased isolation and difficulty moving forward.",
    gender: "male"
  },
  {
    name: "Daniel",
    description: "Highly driven, experiencing work burnout.",
    behavior: "You've always been highly driven and focused on achieving success at work. Recently, you've noticed signs of burnout, such as constant fatigue, irritability, and a lack of motivation. You struggle to balance work and personal life, often prioritizing work over self-care. You fear taking time off, believing it might hurt your career, but deep down, you feel mentally and physically drained.",
    gender: "male"
  },
  {
    name: "Ryan",
    description: "Struggling with recent job loss and financial stress.",
    behavior: "You've recently lost your job and are struggling with financial stress. You feel overwhelmed and uncertain about your future. You're trying to regain confidence and navigate career uncertainty.",
    gender: "male"
  },
  {
    name: "Tom",
    description: "Struggling with social anxiety, avoiding new situations.",
    behavior: "You struggle with social anxiety, often avoiding new situations and social interactions. You find it hard to open up to others and feel uncomfortable in groups. You're trying to develop social skills and feel comfortable in groups.",
    gender: "male"
  },
  {
    name: "Leo",
    description: "Father of three, overwhelmed by parental responsibilities.",
    behavior: "You're a father of three and are overwhelmed by the responsibilities that come with being a parent. You feel stressed and struggle to balance work and family life. You're trying to manage stress and be more present for your family.",
    gender: "male"
  },
  {
    name: "Chris",
    description: "Struggling with unhealthy coping mechanisms, seeking change.",
    behavior: "You're struggling with unhealthy coping mechanisms and are seeking change. You're trying to develop healthier stress-management strategies.",
    gender: "male"
  },
  {
    name: "Ben",
    description: "Overworked and feeling unappreciated at work.",
    behavior: "You're overworked and feel unappreciated at work. You're trying to find fulfillment and recognition in your career.",
    gender: "male"
  },
  {
    name: "Alex",
    description: "In a toxic relationship, struggling to find personal space.",
    behavior: "You're in a toxic relationship and are struggling to find personal space. You're trying to regain independence and establish boundaries.",
    gender: "male"
  },
  {
    name: "Michael",
    description: "Experiencing high stress, trying to keep up with expectations.",
    behavior: "You're experiencing high stress and are trying to keep up with expectations. You're trying to manage stress and set realistic expectations.",
    gender: "male"
  },
  {
    name: "James",
    description: "Struggling with grief after the loss of a loved one.",
    behavior: "You're struggling with grief after the loss of a loved one. You're trying to process your emotions and find ways to heal.",
    gender: "male"
  },

  // Female Personas
  {
    name: "Sarah",
    description: "Newly single, struggling with identity after breakup.",
    behavior: "You're newly single and are struggling with identity after a breakup. You're trying to rebuild your self-confidence and independence.",
    gender: "female"
  },
  {
    name: "Emma",
    description: "Career-driven, struggling with work-life balance.",
    behavior: "You're career-driven and are struggling with work-life balance. You're trying to balance ambition with personal well-being.",
    gender: "female"
  },
  {
    name: "Olivia",
    description: "Struggling with self-esteem and body image issues.",
    behavior: "You're struggling with self-esteem and body image issues. You're trying to develop self-acceptance and confidence.",
    gender: "female"
  },
  {
    name: "Rachel",
    description: "Mother of a newborn, struggling with postpartum anxiety.",
    behavior: "You're a mother of a newborn and are struggling with postpartum anxiety. You're trying to manage anxiety and build confidence in parenting.",
    gender: "female"
  },
  {
    name: "Jessica",
    description: "Recovering from past trauma, struggling with trust.",
    behavior: "You're recovering from past trauma and are struggling with trust. You're trying to heal and feel safe in relationships.",
    gender: "female"
  },
  {
    name: "Sophie",
    description: "Struggling with chronic illness, feeling isolated.",
    behavior: "You're struggling with chronic illness and are feeling isolated. You're trying to connect with others and find emotional support.",
    gender: "female"
  },
  {
    name: "Lily",
    description: "Experiencing burnout from caring for an elderly parent.",
    behavior: "You're experiencing burnout from caring for an elderly parent. You're trying to manage caregiving stress and prioritize self-care.",
    gender: "female"
  },
  {
    name: "Hannah",
    description: "Recently moved to a new city, struggling to make friends.",
    behavior: "You've recently moved to a new city and are struggling to make friends. You're trying to build new social connections and feel at home.",
    gender: "female"
  },
  {
    name: "Mia",
    description: "Dealing with perfectionism, struggling to accept flaws.",
    behavior: "You're dealing with perfectionism and are struggling to accept flaws. You're trying to let go of unrealistic expectations and embrace imperfection.",
    gender: "female"
  },
  {
    name: "Grace",
    description: "Feeling stuck in repetitive behaviors, searching for control.",
    behavior: "You're feeling stuck in repetitive behaviors and are searching for control. You're trying to develop healthier habits and regain a sense of stability.",
    gender: "female"
  }
];

const DIFFICULTY_LEVELS = {
  beginner: {
    title: "Beginner",
    description: "Surface-level concerns, receptive and responsive, focusing on basic issues. Allows practicing basic trust-building and straightforward techniques."
  },
  intermediate: {
    title: "Intermediate",
    description: "Deeper emotional layers, some resistance, requires additional support. More guarded responses and needs time to open up."
  },
  advanced: {
    title: "Advanced",
    description: "Deep-rooted trauma, significant emotional processing, requires advanced techniques. Slower progress, needs persistent and patient approach."
  }
} as const;

type DifficultyLevel = keyof typeof DIFFICULTY_LEVELS;

export default function NewSessionPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [difficulty, setDifficulty] = useState<DifficultyLevel>("beginner");

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      behavior: "",
      gender: "male",
      difficulty: "beginner",
      sessionDuration: "60",
    },
  });

  const handlePatientSelect = (patientName: string) => {
    const selectedPatient = defaultPatients.find(patient => patient.name === patientName);
    if (selectedPatient) {
      form.setValue("name", selectedPatient.name);
      form.setValue("description", selectedPatient.description);
      form.setValue("behavior", selectedPatient.behavior);
      form.setValue("gender", selectedPatient.gender as "male" | "female");
    }
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      
      // Create the agent
      const response = await fetch("/api/agents", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: values.name,
          background: values.description,
          behavior: values.behavior,
          gender: values.gender,
          difficulty: values.difficulty,
          personality: values.behavior, // Using behavior as personality
          concerns: [values.description], // Using description as main concern
          sessionDuration: parseInt(values.sessionDuration)
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create agent');
      }

      const agent = await response.json();
      
      // Create session
      const sessionResponse = await fetch("/api/sessions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          agentId: agent._id,
          duration: parseInt(values.sessionDuration) * 60, // Convert to seconds
          startTime: new Date().toISOString(), // Add valid start time
        }),
      });

      if (!sessionResponse.ok) {
        throw new Error("Failed to create session");
      }

      // Ensure we have a valid agent ID before navigation
      if (!agent._id) {
        throw new Error("Invalid agent ID received");
      }

      // Use replace instead of push to prevent back navigation to the form
      router.replace(`/dashboard/session/${agent._id}`);
    } catch (error) {
      console.error("Error creating agent:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create practice client",
        variant: "destructive",
        message: "error creating agent"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-2xl space-y-8 p-4 sm:p-6 md:p-8">
      <div className="pt-4 sm:pt-6">
        <h1 className="text-3xl font-bold tracking-tight">Create New Session</h1>
        <p className="text-muted-foreground">
          Create a new AI client for your practice session
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Client Name</FormLabel>
                <Select onValueChange={handlePatientSelect}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a default patient" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {defaultPatients.map((patient, index) => (
                      <SelectItem key={index} value={patient.name}>
                        {patient.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormControl>
                  <Input placeholder="Enter client name" {...field} />
                </FormControl>
                <FormDescription>
                  This will be your AI client&apos;s name during the session
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Brief description of the client's situation"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  A short summary of the client's current situation
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="behavior"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Behavior</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Detailed description of how the client behaves"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Detailed description of the client's behavior and responses
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="gender"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Gender</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Choose the voice gender for the AI client
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="difficulty"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Difficulty Level</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Choose the complexity level of the client's case
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="sessionDuration"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Session Duration</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="45">45 minutes</SelectItem>
                    <SelectItem value="60">60 minutes</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Choose how long the session should last
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Creating..." : "Create Session"}
          </Button>
        </form>
      </Form>
    </div>
  );
}